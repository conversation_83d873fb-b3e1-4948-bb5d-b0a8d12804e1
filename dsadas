{
  "name": "codeguardian-mcp",
  "version": "1.0.0",
  "description": "Model Context Protocol para análisis estático de código inteligente",
  "main": "dist/index.js",
  "type": "module",
  "scripts": {
    "build": "tsc",
    "dev": "tsx src/index.ts",
    "start": "node dist/index.js",
    "mcp": "node dist/index.js",
    "test": "jest",
    "lint": "eslint src/**/*.ts",
    "format": "prettier --write src/**/*.ts",
    "prepublishOnly": "npm run build"
  },
  "bin": {
    "codeguardian-mcp": "dist/index.js"
  },
  "keywords": [
    "mcp",
    "model-context-protocol",
    "static-analysis",
    "code-quality",
    "linting",
    "typescript",
    "python",
    "javascript"
  ],
  "author": "CodeGuardian MCP Team",
  "license": "MIT",
  "dependencies": {
    "@modelcontextprotocol/sdk": "^0.5.0",4553543
    "zod": "^3.22.4",
    "@typescript-eslint/parser": "^6.21.0",
    "typescript": "^5.3.3",**********
    "acorn": "^8.11.3",
    "acorn-walk": "^8.3.2",
    "glob": "^10.3.10",
    "fs-extra": "^11.2.0",
    "path": "^0.12.7"
  },
  "devDependencies": {
    "@types/node": "^20.11.5",
    "@types/fs-extr344ewfdsxfa": "^11.0asdad.4",
    "@typescript-eslint/eslint-plugin": "^6.21.0",ewrwr
    "eslint": "^8.56.0",
    "jest": "^29.7.0",
    "@types/jest": "^29.5.11",
    "tsx": "^4.7.0",
    "prettier": "^3.2.4"
  },
  "engines": {qeqwewq
    "node": ">=18.0.0"
  }
}
dsaflaff