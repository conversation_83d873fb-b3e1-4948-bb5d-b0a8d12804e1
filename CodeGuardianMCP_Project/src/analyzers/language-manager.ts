import { ILanguageAnalyzer } from './base-analyzer.js';
import { PythonAnalyzer } from './python-analyzer.js';
import { TypeScriptAnalyzer } from './typescript-analyzer.js';
import { JSONAnalyzer } from './json-analyzer.js';
import { FileInfo, Issue, SupportedLanguage, FileAnalysisResult } from '../types.js';
import { ExtendedAnalysisConfig } from '../config/schemas.js';
import * as fs from 'fs-extra';

/**
 * Gestor central de analizadores de lenguaje
 * Coordina el análisis de archivos según su lenguaje
 */
export class LanguageManager {
  private analyzers: Map<SupportedLanguage, ILanguageAnalyzer>;

  constructor() {
    this.analyzers = new Map();
    this.initializeAnalyzers();
  }

  /**
   * Inicializa todos los analizadores disponibles
   */
  private initializeAnalyzers(): void {
    // Registrar analizadores para cada lenguaje soportado
    this.registerAnalyzer(new PythonAnalyzer());
    this.registerAnalyzer(new TypeScriptAnalyzer());
    this.registerAnalyzer(new JSONAnalyzer());

    // El analizador de TypeScript también maneja JavaScript
    const tsAnalyzer = new TypeScriptAnalyzer();
    this.analyzers.set('javascript', tsAnalyzer);

    console.log(`✅ Analizadores inicializados para: ${Array.from(this.analyzers.keys()).join(', ')}`);
  }

  /**
   * Registra un analizador para un lenguaje específico
   */
  private registerAnalyzer(analyzer: ILanguageAnalyzer): void {
    this.analyzers.set(analyzer.language, analyzer);
    console.log(`📝 Analizador registrado para ${analyzer.language}`);
  }

  /**
   * Obtiene el analizador apropiado para un archivo
   */
  getAnalyzer(language: SupportedLanguage): ILanguageAnalyzer | null {
    return this.analyzers.get(language) || null;
  }

  /**
   * Verifica si un lenguaje está soportado
   */
  isLanguageSupported(language: SupportedLanguage): boolean {
    return this.analyzers.has(language);
  }

  /**
   * Obtiene la lista de lenguajes soportados
   */
  getSupportedLanguages(): SupportedLanguage[] {
    return Array.from(this.analyzers.keys());
  }

  /**
   * Analiza un archivo individual
   */
  async analyzeFile(fileInfo: FileInfo, config: ExtendedAnalysisConfig): Promise<FileAnalysisResult> {
    const startTime = Date.now();
    
    try {
      console.log(`🔍 Analizando archivo: ${fileInfo.path} (${fileInfo.language})`);

      // Obtener el analizador apropiado
      const analyzer = this.getAnalyzer(fileInfo.language);
      if (!analyzer) {
        throw new Error(`No hay analizador disponible para el lenguaje: ${fileInfo.language}`);
      }

      // Verificar que el analizador puede manejar este archivo
      if (!analyzer.canAnalyze(fileInfo)) {
        throw new Error(`El analizador de ${fileInfo.language} no puede procesar este archivo`);
      }

      // Leer el contenido del archivo
      const content = await this.readFileContent(fileInfo.path);

      // Realizar el análisis
      const issues = await analyzer.analyzeFile(fileInfo, content, config);

      // Agregar snippets de código si está habilitado
      if (config.reporting.include_code_snippets) {
        await this.addCodeSnippets(issues, content, config.reporting.snippet_context_lines);
      }

      const duration = Date.now() - startTime;
      
      console.log(`✅ Análisis completado: ${issues.length} issues encontrados en ${duration}ms`);

      return {
        file_info: fileInfo,
        issues,
        analysis_duration_ms: duration,
        success: true
      };

    } catch (error) {
      const duration = Date.now() - startTime;
      const errorMessage = error instanceof Error ? error.message : String(error);
      
      console.error(`❌ Error analizando ${fileInfo.path}: ${errorMessage}`);

      // Crear un issue para reportar el error de análisis
      const errorIssue: Issue = {
        type: 'SyntaxError',
        severity: 'error',
        message: `Error durante el análisis: ${errorMessage}`,
        file_path: fileInfo.path,
        line: 1,
        column: 0,
        suggestion: 'Verifica que el archivo sea válido y accesible',
        language: fileInfo.language
      };

      return {
        file_info: fileInfo,
        issues: [errorIssue],
        analysis_duration_ms: duration,
        success: false
      };
    }
  }

  /**
   * Analiza múltiples archivos en paralelo
   */
  async analyzeFiles(fileInfos: FileInfo[], config: ExtendedAnalysisConfig): Promise<FileAnalysisResult[]> {
    console.log(`🚀 Iniciando análisis de ${fileInfos.length} archivos`);
    
    const maxConcurrent = config.performance.max_concurrent_files;
    const results: FileAnalysisResult[] = [];

    // Procesar archivos en lotes para controlar la concurrencia
    for (let i = 0; i < fileInfos.length; i += maxConcurrent) {
      const batch = fileInfos.slice(i, i + maxConcurrent);
      
      console.log(`📦 Procesando lote ${Math.floor(i / maxConcurrent) + 1}/${Math.ceil(fileInfos.length / maxConcurrent)} (${batch.length} archivos)`);
      
      const batchPromises = batch.map(fileInfo => 
        this.analyzeFileWithTimeout(fileInfo, config)
      );

      const batchResults = await Promise.all(batchPromises);
      results.push(...batchResults);
    }

    const totalIssues = results.reduce((sum, result) => sum + result.issues.length, 0);
    const successfulAnalyses = results.filter(r => r.success).length;
    
    console.log(`✅ Análisis completado: ${successfulAnalyses}/${fileInfos.length} archivos exitosos, ${totalIssues} issues totales`);

    return results;
  }

  /**
   * Analiza un archivo con timeout
   */
  private async analyzeFileWithTimeout(fileInfo: FileInfo, config: ExtendedAnalysisConfig): Promise<FileAnalysisResult> {
    const timeoutMs = config.performance.timeout_per_file_ms;
    
    return Promise.race([
      this.analyzeFile(fileInfo, config),
      new Promise<FileAnalysisResult>((_, reject) => 
        setTimeout(() => reject(new Error(`Timeout después de ${timeoutMs}ms`)), timeoutMs)
      )
    ]).catch(error => {
      console.warn(`⚠️  Timeout o error en ${fileInfo.path}: ${error.message}`);
      
      return {
        file_info: fileInfo,
        issues: [{
          type: 'SyntaxError',
          severity: 'error',
          message: `Análisis interrumpido: ${error.message}`,
          file_path: fileInfo.path,
          line: 1,
          column: 0,
          suggestion: 'El archivo puede ser demasiado complejo o contener errores que impiden el análisis',
          language: fileInfo.language
        }],
        analysis_duration_ms: timeoutMs,
        success: false
      };
    });
  }

  /**
   * Lee el contenido de un archivo de forma segura
   */
  private async readFileContent(filePath: string): Promise<string> {
    try {
      const content = await fs.readFile(filePath, 'utf-8');
      return content;
    } catch (error) {
      if (error instanceof Error && 'code' in error) {
        if (error.code === 'ENOENT') {
          throw new Error(`Archivo no encontrado: ${filePath}`);
        } else if (error.code === 'EACCES') {
          throw new Error(`Sin permisos para leer el archivo: ${filePath}`);
        } else if (error.code === 'EISDIR') {
          throw new Error(`La ruta es un directorio, no un archivo: ${filePath}`);
        }
      }
      throw new Error(`Error leyendo archivo ${filePath}: ${error instanceof Error ? error.message : String(error)}`);
    }
  }

  /**
   * Agrega snippets de código a los issues
   */
  private async addCodeSnippets(issues: Issue[], content: string, contextLines: number): Promise<void> {
    const lines = content.split('\n');

    for (const issue of issues) {
      if (!issue.code_snippet) {
        const startLine = Math.max(0, issue.line - contextLines - 1);
        const endLine = Math.min(lines.length - 1, issue.line + contextLines - 1);
        
        const snippetLines = lines.slice(startLine, endLine + 1);
        const snippet = snippetLines.map((line, index) => {
          const lineNum = startLine + index + 1;
          const marker = lineNum === issue.line ? ' --> ' : '     ';
          return `${lineNum.toString().padStart(4)}${marker}${line}`;
        }).join('\n');

        issue.code_snippet = snippet;
      }
    }
  }

  /**
   * Obtiene estadísticas de los analizadores
   */
  getAnalyzerStats(): Record<string, any> {
    return {
      total_analyzers: this.analyzers.size,
      supported_languages: this.getSupportedLanguages(),
      analyzer_details: Array.from(this.analyzers.entries()).map(([language, analyzer]) => ({
        language,
        analyzer_class: analyzer.constructor.name
      }))
    };
  }

  /**
   * Valida la configuración para un lenguaje específico
   */
  validateLanguageConfig(language: SupportedLanguage, config: ExtendedAnalysisConfig): string[] {
    const errors: string[] = [];
    
    if (!this.isLanguageSupported(language)) {
      errors.push(`Lenguaje no soportado: ${language}`);
      return errors;
    }

    // Validaciones específicas por lenguaje
    switch (language) {
      case 'python':
        if (config.language_configs.python?.python_version) {
          const version = config.language_configs.python.python_version;
          if (!/^\d+\.\d+$/.test(version)) {
            errors.push(`Versión de Python inválida: ${version}`);
          }
        }
        break;
        
      case 'typescript':
        if (config.language_configs.typescript?.typescript_version) {
          const version = config.language_configs.typescript.typescript_version;
          if (!/^\d+\.\d+$/.test(version)) {
            errors.push(`Versión de TypeScript inválida: ${version}`);
          }
        }
        break;
        
      case 'javascript':
        if (config.language_configs.javascript?.ecma_version) {
          const version = config.language_configs.javascript.ecma_version;
          if (version < 5 || version > 2023) {
            errors.push(`Versión de ECMAScript inválida: ${version}`);
          }
        }
        break;
    }

    return errors;
  }
}
