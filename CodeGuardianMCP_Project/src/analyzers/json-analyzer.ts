import { BaseLanguageAnalyzer } from './base-analyzer.js';
import { Issue, SupportedLanguage } from '../types.js';
import { ExtendedAnalysisConfig } from '../config/schemas.js';
import * as fs from 'fs-extra';
import * as path from 'path';

/**
 * Analizador específico para archivos JSON
 * Maneja validación de sintaxis, estructura y contenido específico
 */
export class JSONAnalyzer extends BaseLanguageAnalyzer {
  readonly language: SupportedLanguage = 'json';

  /**
   * Valida la sintaxis JSON del archivo
   */
  async validateSyntax(content: string, filePath: string): Promise<Issue[]> {
    const issues: Issue[] = [];

    try {
      // Intentar parsear el JSON
      JSON.parse(content);
      
      // Si llegamos aquí, el JSON es válido
      console.log(`✅ JSON válido: ${filePath}`);
      
    } catch (error: any) {
      // Parsear el error de JSON para obtener información detallada
      const jsonIssue = this.parseJSONError(error, filePath, content);
      if (jsonIssue) {
        issues.push(jsonIssue);
      }
    }

    // Validaciones adicionales específicas para JSON
    const structureIssues = await this.validateJSONStructure(content, filePath);
    issues.push(...structureIssues);

    return issues;
  }

  /**
   * Valida imports/requires (no aplica para JSON, pero requerido por la interfaz)
   */
  async validateImports(_content: string, _filePath: string, _config: ExtendedAnalysisConfig): Promise<Issue[]> {
    // JSON no tiene imports, retornar array vacío
    return [];
  }

  /**
   * Valida rutas referenciadas en el archivo JSON
   */
  async validatePaths(content: string, filePath: string): Promise<Issue[]> {
    const issues: Issue[] = [];

    try {
      const jsonData = JSON.parse(content);
      
      // Validar rutas específicas según el tipo de archivo JSON
      if (this.isPackageJSON(filePath)) {
        const packageIssues = await this.validatePackageJSONPaths(jsonData, filePath);
        issues.push(...packageIssues);
      }
      
      // Buscar rutas de archivos en valores de string
      const pathIssues = await this.findAndValidateFilePaths(jsonData, filePath);
      issues.push(...pathIssues);
      
    } catch (error) {
      // Si no se puede parsear el JSON, no validar rutas
      // El error de sintaxis ya se reportó en validateSyntax
    }

    return issues;
  }

  /**
   * Parsea errores de JSON.parse para crear issues detallados
   */
  private parseJSONError(error: any, filePath: string, content: string): Issue | null {
    const message = error.message || 'Error de sintaxis JSON';
    
    // Extraer información de posición del error
    let line = 1;
    let column = 0;
    let suggestion = 'Verifica la sintaxis JSON';

    // Buscar patrones comunes en mensajes de error de JSON
    const positionMatch = message.match(/at position (\d+)/);
    if (positionMatch) {
      const position = parseInt(positionMatch[1]);
      const lineInfo = this.getLineColumnFromPosition(content, position);
      line = lineInfo.line;
      column = lineInfo.column;
    }

    // Sugerencias específicas basadas en el tipo de error
    if (message.includes('Unexpected token')) {
      const tokenMatch = message.match(/Unexpected token (.)/);
      if (tokenMatch) {
        const token = tokenMatch[1];
        suggestion = this.getSuggestionForUnexpectedToken(token, content, line);
      }
    } else if (message.includes('Unexpected end of JSON input')) {
      suggestion = 'El archivo JSON está incompleto. Verifica que todas las llaves y corchetes estén cerrados.';
    } else if (message.includes('Unexpected string')) {
      suggestion = 'String inesperado. Verifica que no falten comas entre elementos o que no haya comas extra.';
    }

    return {
      type: 'SyntaxError',
      severity: 'error',
      message: `Error de sintaxis JSON: ${message}`,
      file_path: filePath,
      line,
      column,
      suggestion,
      language: this.language,
      code_snippet: this.extractCodeSnippet(content, line, 3)
    };
  }

  /**
   * Convierte una posición de carácter a línea y columna
   */
  private getLineColumnFromPosition(content: string, position: number): { line: number; column: number } {
    const lines = content.substring(0, position).split('\n');
    const line = lines.length;
    const column = lines[lines.length - 1]?.length || 0;
    
    return { line, column };
  }

  /**
   * Genera sugerencias específicas para tokens inesperados
   */
  private getSuggestionForUnexpectedToken(token: string, _content: string, line: number): string {
    switch (token) {
      case ',':
        return 'Coma inesperada. Verifica que no haya comas extra al final de objetos o arrays.';
      case '}':
        return 'Llave de cierre inesperada. Verifica que no falten comas entre propiedades.';
      case ']':
        return 'Corchete de cierre inesperado. Verifica que no falten comas entre elementos del array.';
      case '"':
        return 'Comilla inesperada. Verifica que todas las strings estén correctamente cerradas.';
      default:
        return `Token inesperado '${token}'. Verifica la sintaxis JSON alrededor de la línea ${line}.`;
    }
  }

  /**
   * Valida la estructura general del JSON
   */
  private async validateJSONStructure(content: string, filePath: string): Promise<Issue[]> {
    const issues: Issue[] = [];

    try {
      const jsonData = JSON.parse(content);
      
      // Validaciones específicas según el tipo de archivo
      if (this.isPackageJSON(filePath)) {
        const packageIssues = await this.validatePackageJSONStructure(jsonData, filePath);
        issues.push(...packageIssues);
      } else if (this.isTSConfigJSON(filePath)) {
        const tsconfigIssues = await this.validateTSConfigStructure(jsonData, filePath);
        issues.push(...tsconfigIssues);
      }
      
    } catch (error) {
      // Error de parsing ya manejado en validateSyntax
    }

    return issues;
  }

  /**
   * Verifica si es un archivo package.json
   */
  private isPackageJSON(filePath: string): boolean {
    return path.basename(filePath).toLowerCase() === 'package.json';
  }

  /**
   * Verifica si es un archivo tsconfig.json
   */
  private isTSConfigJSON(filePath: string): boolean {
    const basename = path.basename(filePath).toLowerCase();
    return basename === 'tsconfig.json' || basename.includes('tsconfig');
  }

  /**
   * Valida la estructura específica de package.json
   */
  private async validatePackageJSONStructure(packageData: any, filePath: string): Promise<Issue[]> {
    const issues: Issue[] = [];

    // Validar campos requeridos
    if (!packageData.name) {
      issues.push(this.createWarningIssue(
        'Campo "name" faltante en package.json',
        filePath,
        1,
        0,
        'Añade un campo "name" con el nombre del paquete'
      ));
    }

    if (!packageData.version) {
      issues.push(this.createWarningIssue(
        'Campo "version" faltante en package.json',
        filePath,
        1,
        0,
        'Añade un campo "version" siguiendo el formato semver (ej: "1.0.0")'
      ));
    }

    // Validar formato de versión
    if (packageData.version && !this.isValidSemver(packageData.version)) {
      issues.push(this.createWarningIssue(
        `Formato de versión inválido: "${packageData.version}"`,
        filePath,
        1,
        0,
        'Usa formato semver válido (ej: "1.0.0", "2.1.3-beta.1")'
      ));
    }

    // Validar dependencias
    if (packageData.dependencies) {
      const depIssues = this.validateDependencies(packageData.dependencies, filePath, 'dependencies');
      issues.push(...depIssues);
    }

    if (packageData.devDependencies) {
      const devDepIssues = this.validateDependencies(packageData.devDependencies, filePath, 'devDependencies');
      issues.push(...devDepIssues);
    }

    // Validar scripts
    if (packageData.scripts) {
      const scriptIssues = this.validateScripts(packageData.scripts, filePath);
      issues.push(...scriptIssues);
    }

    return issues;
  }

  /**
   * Valida formato semver
   */
  private isValidSemver(version: string): boolean {
    const semverRegex = /^(\d+)\.(\d+)\.(\d+)(?:-([0-9A-Za-z-]+(?:\.[0-9A-Za-z-]+)*))?(?:\+([0-9A-Za-z-]+(?:\.[0-9A-Za-z-]+)*))?$/;
    return semverRegex.test(version);
  }

  /**
   * Valida dependencias en package.json
   */
  private validateDependencies(dependencies: any, filePath: string, section: string): Issue[] {
    const issues: Issue[] = [];

    if (typeof dependencies !== 'object' || dependencies === null) {
      issues.push(this.createErrorIssue(
        `Sección "${section}" debe ser un objeto`,
        filePath,
        1,
        0,
        `Cambia "${section}" a un objeto con pares clave-valor`
      ));
      return issues;
    }

    for (const [packageName, version] of Object.entries(dependencies)) {
      if (typeof version !== 'string') {
        issues.push(this.createWarningIssue(
          `Versión inválida para "${packageName}": debe ser string`,
          filePath,
          1,
          0,
          `Cambia la versión de "${packageName}" a un string válido`
        ));
        continue;
      }

      // Validar formato de nombre de paquete
      if (!this.isValidPackageName(packageName)) {
        issues.push(this.createWarningIssue(
          `Nombre de paquete inválido: "${packageName}"`,
          filePath,
          1,
          0,
          'Los nombres de paquetes deben seguir las reglas de npm'
        ));
      }

      // Validar formato de versión
      if (!this.isValidVersionRange(version as string)) {
        issues.push(this.createWarningIssue(
          `Rango de versión inválido para "${packageName}": "${version}"`,
          filePath,
          1,
          0,
          'Usa un rango de versión válido (ej: "^1.0.0", "~2.1.0", ">=3.0.0")'
        ));
      }
    }

    return issues;
  }

  /**
   * Valida nombre de paquete npm
   */
  private isValidPackageName(name: string): boolean {
    // Reglas básicas de npm package names
    const npmNameRegex = /^(?:@[a-z0-9-*~][a-z0-9-*._~]*\/)?[a-z0-9-~][a-z0-9-._~]*$/;
    return npmNameRegex.test(name) && name.length <= 214;
  }

  /**
   * Valida rango de versión npm
   */
  private isValidVersionRange(version: string): boolean {
    // Patrones comunes de versiones npm
    const versionPatterns = [
      /^\d+\.\d+\.\d+$/, // Exacta: 1.0.0
      /^\^\d+\.\d+\.\d+/, // Caret: ^1.0.0
      /^~\d+\.\d+\.\d+/, // Tilde: ~1.0.0
      /^>=\d+\.\d+\.\d+/, // Mayor o igual: >=1.0.0
      /^>\d+\.\d+\.\d+/, // Mayor: >1.0.0
      /^<=\d+\.\d+\.\d+/, // Menor o igual: <=1.0.0
      /^<\d+\.\d+\.\d+/, // Menor: <1.0.0
      /^\*$/, // Cualquiera: *
      /^latest$/, // Latest
      /^next$/, // Next
      /^file:/, // File protocol
      /^git\+/, // Git protocol
      /^https?:\/\//, // HTTP/HTTPS
    ];

    return versionPatterns.some(pattern => pattern.test(version));
  }

  /**
   * Valida scripts en package.json
   */
  private validateScripts(scripts: any, filePath: string): Issue[] {
    const issues: Issue[] = [];

    if (typeof scripts !== 'object' || scripts === null) {
      issues.push(this.createErrorIssue(
        'Sección "scripts" debe ser un objeto',
        filePath,
        1,
        0,
        'Cambia "scripts" a un objeto con pares clave-valor'
      ));
      return issues;
    }

    for (const [scriptName, command] of Object.entries(scripts)) {
      if (typeof command !== 'string') {
        issues.push(this.createWarningIssue(
          `Script "${scriptName}" debe ser un string`,
          filePath,
          1,
          0,
          `Cambia el comando del script "${scriptName}" a un string`
        ));
      }
    }

    return issues;
  }

  /**
   * Valida rutas específicas en package.json
   */
  private async validatePackageJSONPaths(packageData: any, filePath: string): Promise<Issue[]> {
    const issues: Issue[] = [];
    const packageDir = path.dirname(filePath);

    // Validar campo "main"
    if (packageData.main) {
      const mainPath = path.resolve(packageDir, packageData.main);
      const exists = await fs.pathExists(mainPath);
      if (!exists) {
        issues.push(this.createPathIssue(
          `Archivo principal no encontrado: "${packageData.main}"`,
          filePath,
          1,
          0,
          `Verifica que el archivo existe en: ${mainPath}`
        ));
      }
    }

    // Validar campo "bin"
    if (packageData.bin) {
      if (typeof packageData.bin === 'string') {
        const binPath = path.resolve(packageDir, packageData.bin);
        const exists = await fs.pathExists(binPath);
        if (!exists) {
          issues.push(this.createPathIssue(
            `Archivo binario no encontrado: "${packageData.bin}"`,
            filePath,
            1,
            0,
            `Verifica que el archivo existe en: ${binPath}`
          ));
        }
      } else if (typeof packageData.bin === 'object') {
        for (const [binName, binPath] of Object.entries(packageData.bin)) {
          if (typeof binPath === 'string') {
            const fullBinPath = path.resolve(packageDir, binPath);
            const exists = await fs.pathExists(fullBinPath);
            if (!exists) {
              issues.push(this.createPathIssue(
                `Archivo binario "${binName}" no encontrado: "${binPath}"`,
                filePath,
                1,
                0,
                `Verifica que el archivo existe en: ${fullBinPath}`
              ));
            }
          }
        }
      }
    }

    return issues;
  }

  /**
   * Busca y valida rutas de archivos en valores JSON
   */
  private async findAndValidateFilePaths(jsonData: any, filePath: string): Promise<Issue[]> {
    const issues: Issue[] = [];
    const jsonDir = path.dirname(filePath);

    // Función recursiva para buscar rutas en el objeto JSON
    const searchPaths = (obj: any, currentPath: string[] = []): void => {
      if (typeof obj === 'string' && this.looksLikeFilePath(obj)) {
        this.validateFilePath(obj, filePath, jsonDir, issues);
      } else if (Array.isArray(obj)) {
        obj.forEach((item, index) => {
          searchPaths(item, [...currentPath, index.toString()]);
        });
      } else if (obj && typeof obj === 'object') {
        Object.entries(obj).forEach(([key, value]) => {
          searchPaths(value, [...currentPath, key]);
        });
      }
    };

    searchPaths(jsonData);
    return issues;
  }

  /**
   * Verifica si un string parece una ruta de archivo
   */
  private looksLikeFilePath(str: string): boolean {
    // Patrones que sugieren rutas de archivo
    const pathPatterns = [
      /^\.{1,2}\//, // Rutas relativas: ./ o ../
      /^\//, // Rutas absolutas
      /\.(js|ts|json|md|txt|css|html|xml|yml|yaml)$/i, // Extensiones comunes
      /^[a-zA-Z]:[\\\/]/, // Rutas de Windows
    ];

    return pathPatterns.some(pattern => pattern.test(str)) &&
           str.length > 2 &&
           !str.startsWith('http') &&
           !str.startsWith('git+');
  }

  /**
   * Valida si una ruta de archivo existe
   */
  private async validateFilePath(pathStr: string, jsonFilePath: string, jsonDir: string, issues: Issue[]): Promise<void> {
    try {
      let fullPath: string;

      if (path.isAbsolute(pathStr)) {
        fullPath = pathStr;
      } else {
        fullPath = path.resolve(jsonDir, pathStr);
      }

      const exists = await fs.pathExists(fullPath);
      if (!exists) {
        issues.push(this.createPathIssue(
          `Ruta de archivo no encontrada: "${pathStr}"`,
          jsonFilePath,
          1,
          0,
          `Verifica que el archivo existe en: ${fullPath}`
        ));
      }
    } catch (error) {
      issues.push(this.createPathIssue(
        `Error validando ruta "${pathStr}": ${error instanceof Error ? error.message : String(error)}`,
        jsonFilePath,
        1,
        0,
        'Verifica que la ruta sea válida'
      ));
    }
  }

  /**
   * Valida estructura de tsconfig.json
   */
  private async validateTSConfigStructure(tsconfigData: any, filePath: string): Promise<Issue[]> {
    const issues: Issue[] = [];

    // Validar que tenga la estructura básica
    if (!tsconfigData.compilerOptions && !tsconfigData.extends) {
      issues.push(this.createWarningIssue(
        'tsconfig.json debe tener "compilerOptions" o "extends"',
        filePath,
        1,
        0,
        'Añade una sección "compilerOptions" con las opciones del compilador'
      ));
    }

    // Validar compilerOptions si existe
    if (tsconfigData.compilerOptions) {
      const compilerIssues = this.validateCompilerOptions(tsconfigData.compilerOptions, filePath);
      issues.push(...compilerIssues);
    }

    // Validar extends si existe
    if (tsconfigData.extends) {
      const extendsIssues = await this.validateTSConfigExtends(tsconfigData.extends, filePath);
      issues.push(...extendsIssues);
    }

    return issues;
  }

  /**
   * Valida opciones del compilador TypeScript
   */
  private validateCompilerOptions(compilerOptions: any, filePath: string): Issue[] {
    const issues: Issue[] = [];

    if (typeof compilerOptions !== 'object' || compilerOptions === null) {
      issues.push(this.createErrorIssue(
        '"compilerOptions" debe ser un objeto',
        filePath,
        1,
        0,
        'Cambia "compilerOptions" a un objeto con las opciones del compilador'
      ));
      return issues;
    }

    // Validar opciones comunes recomendadas
    const recommendedOptions = {
      strict: 'boolean',
      target: 'string',
      module: 'string',
      moduleResolution: 'string'
    };

    for (const [option, expectedType] of Object.entries(recommendedOptions)) {
      if (compilerOptions[option] !== undefined && typeof compilerOptions[option] !== expectedType) {
        issues.push(this.createWarningIssue(
          `Opción "${option}" debe ser de tipo ${expectedType}`,
          filePath,
          1,
          0,
          `Cambia el valor de "${option}" a un ${expectedType} válido`
        ));
      }
    }

    return issues;
  }

  /**
   * Valida la referencia extends en tsconfig.json
   */
  private async validateTSConfigExtends(extendsValue: any, filePath: string): Promise<Issue[]> {
    const issues: Issue[] = [];

    if (typeof extendsValue !== 'string') {
      issues.push(this.createErrorIssue(
        '"extends" debe ser un string',
        filePath,
        1,
        0,
        'Cambia "extends" a un string con la ruta del archivo base'
      ));
      return issues;
    }

    // Si no es un paquete npm, validar que el archivo existe
    if (!extendsValue.startsWith('@') && extendsValue.includes('/')) {
      const configDir = path.dirname(filePath);
      const extendsPath = path.resolve(configDir, extendsValue);

      const exists = await fs.pathExists(extendsPath);
      if (!exists) {
        issues.push(this.createPathIssue(
          `Archivo de configuración base no encontrado: "${extendsValue}"`,
          filePath,
          1,
          0,
          `Verifica que el archivo existe en: ${extendsPath}`
        ));
      }
    }

    return issues;
  }
}
