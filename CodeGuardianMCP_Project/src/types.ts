import { z } from 'zod';

/**
 * Esquemas de validación usando Zod para CodeGuardian MCP
 */

// Tipos de errores soportados
export const IssueTypeSchema = z.enum([
  'SyntaxError',
  'ImportError', 
  'PathError',
  'DependencyError',
  'TypeError',
  'Warning',
  'StyleError',
  'SecurityError'
]);

export type IssueType = z.infer<typeof IssueTypeSchema>;

// Severidad del issue
export const SeveritySchema = z.enum(['error', 'warning', 'info']);
export type Severity = z.infer<typeof SeveritySchema>;

// Lenguajes soportados
export const SupportedLanguageSchema = z.enum([
  'python',
  'typescript',
  'javascript',
  'json'
]);

export type SupportedLanguage = z.infer<typeof SupportedLanguageSchema>;

// Esquema para un issue individual
export const IssueSchema = z.object({
  type: IssueTypeSchema,
  severity: SeveritySchema,
  message: z.string(),
  file_path: z.string(),
  line: z.number().int().positive(),
  column: z.number().int().nonnegative(),
  code_snippet: z.string().optional(),
  suggestion: z.string().optional(),
  rule_id: z.string().optional(),
  language: SupportedLanguageSchema
});

export type Issue = z.infer<typeof IssueSchema>;

// Esquema para el resumen del análisis
export const AnalysisSummarySchema = z.object({
  files_scanned: z.number().int().nonnegative(),
  errors_found: z.number().int().nonnegative(),
  warnings_found: z.number().int().nonnegative(),
  info_found: z.number().int().nonnegative(),
  languages_detected: z.array(SupportedLanguageSchema),
  scan_duration_ms: z.number().nonnegative(),
  timestamp: z.string().datetime()
});

export type AnalysisSummary = z.infer<typeof AnalysisSummarySchema>;

// Esquema para el reporte completo
export const AnalysisReportSchema = z.object({
  summary: AnalysisSummarySchema,
  issues: z.array(IssueSchema),
  project_path: z.string(),
  config_used: z.record(z.unknown()).optional()
});

export type AnalysisReport = z.infer<typeof AnalysisReportSchema>;

// Configuración del análisis
export const AnalysisConfigSchema = z.object({
  target_path: z.string(),
  include_patterns: z.array(z.string()).default(['**/*.py', '**/*.ts', '**/*.js', '**/*.json']),
  exclude_patterns: z.array(z.string()).default(['**/node_modules/**', '**/.git/**', '**/dist/**', '**/__pycache__/**']),
  max_file_size_mb: z.number().positive().default(10),
  languages: z.array(SupportedLanguageSchema).default(['python', 'typescript', 'javascript', 'json']),
  rules: z.object({
    syntax_check: z.boolean().default(true),
    import_validation: z.boolean().default(true),
    path_validation: z.boolean().default(true),
    dependency_check: z.boolean().default(true),
    style_check: z.boolean().default(false)
  }).default({}),
  output_format: z.enum(['json', 'console', 'both']).default('both')
});

export type AnalysisConfig = z.infer<typeof AnalysisConfigSchema>;

// Información de archivo escaneado
export const FileInfoSchema = z.object({
  path: z.string(),
  language: SupportedLanguageSchema,
  size_bytes: z.number().nonnegative(),
  lines_count: z.number().int().nonnegative(),
  encoding: z.string().default('utf-8')
});

export type FileInfo = z.infer<typeof FileInfoSchema>;

// Resultado del análisis de un archivo individual
export const FileAnalysisResultSchema = z.object({
  file_info: FileInfoSchema,
  issues: z.array(IssueSchema),
  analysis_duration_ms: z.number().nonnegative(),
  success: z.boolean()
});

export type FileAnalysisResult = z.infer<typeof FileAnalysisResultSchema>;

// Parámetros para herramientas MCP
export const AnalyzeProjectParamsSchema = z.object({
  project_path: z.string(),
  config: AnalysisConfigSchema.partial().optional()
});

export type AnalyzeProjectParams = z.infer<typeof AnalyzeProjectParamsSchema>;

export const AnalyzeFileParamsSchema = z.object({
  file_path: z.string(),
  language: SupportedLanguageSchema.optional(),
  config: AnalysisConfigSchema.partial().optional()
});

export type AnalyzeFileParams = z.infer<typeof AnalyzeFileParamsSchema>;

// Respuestas de herramientas MCP
export const ToolResponseSchema = z.object({
  success: z.boolean(),
  data: z.unknown().optional(),
  error: z.string().optional()
});

export type ToolResponse = z.infer<typeof ToolResponseSchema>;
