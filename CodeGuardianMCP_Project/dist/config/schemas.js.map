{"version": 3, "file": "schemas.js", "sourceRoot": "", "sources": ["../../src/config/schemas.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,CAAC,EAAE,MAAM,KAAK,CAAC;AACxB,OAAO,EAAE,oBAAoB,EAAE,MAAM,aAAa,CAAC;AAEnD;;GAEG;AAEH,wCAAwC;AACxC,MAAM,CAAC,MAAM,kBAAkB,GAAG,CAAC,CAAC,MAAM,CAAC;IACzC,cAAc,EAAE,CAAC,CAAC,MAAM,EAAE,CAAC,OAAO,CAAC,KAAK,CAAC;IACzC,aAAa,EAAE,CAAC,CAAC,OAAO,EAAE,CAAC,OAAO,CAAC,IAAI,CAAC;IACxC,YAAY,EAAE,CAAC,CAAC,OAAO,EAAE,CAAC,OAAO,CAAC,IAAI,CAAC;IACvC,WAAW,EAAE,CAAC,CAAC,OAAO,EAAE,CAAC,OAAO,CAAC,KAAK,CAAC;IACvC,eAAe,EAAE,CAAC,CAAC,MAAM,EAAE,CAAC,GAAG,EAAE,CAAC,QAAQ,EAAE,CAAC,OAAO,CAAC,EAAE,CAAC;IACxD,sBAAsB,EAAE,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,MAAM,EAAE,CAAC,CAAC,OAAO,CAAC,EAAE,CAAC;IACvD,gBAAgB,EAAE,CAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,EAAE;CACxC,CAAC,CAAC;AAEH,MAAM,CAAC,MAAM,sBAAsB,GAAG,CAAC,CAAC,MAAM,CAAC;IAC7C,kBAAkB,EAAE,CAAC,CAAC,MAAM,EAAE,CAAC,OAAO,CAAC,KAAK,CAAC;IAC7C,WAAW,EAAE,CAAC,CAAC,OAAO,EAAE,CAAC,OAAO,CAAC,IAAI,CAAC;IACtC,aAAa,EAAE,CAAC,CAAC,OAAO,EAAE,CAAC,OAAO,CAAC,IAAI,CAAC;IACxC,YAAY,EAAE,CAAC,CAAC,OAAO,EAAE,CAAC,OAAO,CAAC,IAAI,CAAC;IACvC,WAAW,EAAE,CAAC,CAAC,OAAO,EAAE,CAAC,OAAO,CAAC,IAAI,CAAC;IACtC,aAAa,EAAE,CAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,EAAE;IACpC,iBAAiB,EAAE,CAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,EAAE;CACzC,CAAC,CAAC;AAEH,MAAM,CAAC,MAAM,sBAAsB,GAAG,CAAC,CAAC,MAAM,CAAC;IAC7C,YAAY,EAAE,CAAC,CAAC,MAAM,EAAE,CAAC,GAAG,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,IAAI,CAAC;IACnD,WAAW,EAAE,CAAC,CAAC,IAAI,CAAC,CAAC,QAAQ,EAAE,QAAQ,CAAC,CAAC,CAAC,OAAO,CAAC,QAAQ,CAAC;IAC3D,aAAa,EAAE,CAAC,CAAC,OAAO,EAAE,CAAC,OAAO,CAAC,IAAI,CAAC;IACxC,YAAY,EAAE,CAAC,CAAC,OAAO,EAAE,CAAC,OAAO,CAAC,IAAI,CAAC;IACvC,SAAS,EAAE,CAAC,CAAC,OAAO,EAAE,CAAC,OAAO,CAAC,KAAK,CAAC;CACtC,CAAC,CAAC;AAEH,sCAAsC;AACtC,MAAM,CAAC,MAAM,gBAAgB,GAAG,CAAC,CAAC,MAAM,CAAC;IACvC,qBAAqB;IACrB,YAAY,EAAE,CAAC,CAAC,MAAM,CAAC;QACrB,gBAAgB,EAAE,CAAC,CAAC,OAAO,EAAE,CAAC,OAAO,CAAC,IAAI,CAAC;QAC3C,cAAc,EAAE,CAAC,CAAC,OAAO,EAAE,CAAC,OAAO,CAAC,IAAI,CAAC;QACzC,iBAAiB,EAAE,CAAC,CAAC,OAAO,EAAE,CAAC,OAAO,CAAC,KAAK,CAAC;KAC9C,CAAC,CAAC,OAAO,CAAC,EAAE,CAAC;IAEd,oBAAoB;IACpB,YAAY,EAAE,CAAC,CAAC,MAAM,CAAC;QACrB,eAAe,EAAE,CAAC,CAAC,OAAO,EAAE,CAAC,OAAO,CAAC,IAAI,CAAC;QAC1C,cAAc,EAAE,CAAC,CAAC,OAAO,EAAE,CAAC,OAAO,CAAC,IAAI,CAAC;QACzC,sBAAsB,EAAE,CAAC,CAAC,OAAO,EAAE,CAAC,OAAO,CAAC,IAAI,CAAC;QACjD,gBAAgB,EAAE,CAAC,CAAC,MAAM,EAAE,CAAC,GAAG,EAAE,CAAC,QAAQ,EAAE,CAAC,OAAO,CAAC,EAAE,CAAC;KAC1D,CAAC,CAAC,OAAO,CAAC,EAAE,CAAC;IAEd,kBAAkB;IAClB,UAAU,EAAE,CAAC,CAAC,MAAM,CAAC;QACnB,oBAAoB,EAAE,CAAC,CAAC,OAAO,EAAE,CAAC,OAAO,CAAC,IAAI,CAAC;QAC/C,iBAAiB,EAAE,CAAC,CAAC,OAAO,EAAE,CAAC,OAAO,CAAC,KAAK,CAAC;QAC7C,kBAAkB,EAAE,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,MAAM,EAAE,CAAC,CAAC,OAAO,CAAC,EAAE,CAAC;QACnD,eAAe,EAAE,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,MAAM,EAAE,CAAC,CAAC,OAAO,CAAC,EAAE,CAAC;KACjD,CAAC,CAAC,OAAO,CAAC,EAAE,CAAC;IAEd,yBAAyB;IACzB,gBAAgB,EAAE,CAAC,CAAC,MAAM,CAAC;QACzB,kBAAkB,EAAE,CAAC,CAAC,OAAO,EAAE,CAAC,OAAO,CAAC,IAAI,CAAC;QAC7C,sBAAsB,EAAE,CAAC,CAAC,OAAO,EAAE,CAAC,OAAO,CAAC,IAAI,CAAC;QACjD,cAAc,EAAE,CAAC,CAAC,OAAO,EAAE,CAAC,OAAO,CAAC,KAAK,CAAC;QAC1C,qBAAqB,EAAE,CAAC,CAAC,OAAO,EAAE,CAAC,OAAO,CAAC,KAAK,CAAC;KAClD,CAAC,CAAC,OAAO,CAAC,EAAE,CAAC;CACf,CAAC,CAAC;AAEH,mCAAmC;AACnC,MAAM,CAAC,MAAM,4BAA4B,GAAG,oBAAoB,CAAC,MAAM,CAAC;IACtE,2CAA2C;IAC3C,gBAAgB,EAAE,CAAC,CAAC,MAAM,CAAC;QACzB,MAAM,EAAE,kBAAkB,CAAC,QAAQ,EAAE;QACrC,UAAU,EAAE,sBAAsB,CAAC,QAAQ,EAAE;QAC7C,UAAU,EAAE,sBAAsB,CAAC,QAAQ,EAAE;KAC9C,CAAC,CAAC,OAAO,CAAC,EAAE,CAAC;IAEd,qCAAqC;IACrC,cAAc,EAAE,gBAAgB,CAAC,OAAO,CAAC,EAAE,CAAC;IAE5C,+BAA+B;IAC/B,WAAW,EAAE,CAAC,CAAC,MAAM,CAAC;QACpB,oBAAoB,EAAE,CAAC,CAAC,MAAM,EAAE,CAAC,GAAG,EAAE,CAAC,QAAQ,EAAE,CAAC,OAAO,CAAC,EAAE,CAAC;QAC7D,mBAAmB,EAAE,CAAC,CAAC,MAAM,EAAE,CAAC,GAAG,EAAE,CAAC,QAAQ,EAAE,CAAC,OAAO,CAAC,KAAK,CAAC;QAC/D,eAAe,EAAE,CAAC,CAAC,MAAM,EAAE,CAAC,GAAG,EAAE,CAAC,QAAQ,EAAE,CAAC,OAAO,CAAC,GAAG,CAAC;KAC1D,CAAC,CAAC,OAAO,CAAC,EAAE,CAAC;IAEd,4BAA4B;IAC5B,SAAS,EAAE,CAAC,CAAC,MAAM,CAAC;QAClB,qBAAqB,EAAE,CAAC,CAAC,OAAO,EAAE,CAAC,OAAO,CAAC,IAAI,CAAC;QAChD,qBAAqB,EAAE,CAAC,CAAC,MAAM,EAAE,CAAC,GAAG,EAAE,CAAC,WAAW,EAAE,CAAC,OAAO,CAAC,CAAC,CAAC;QAChE,aAAa,EAAE,CAAC,CAAC,OAAO,EAAE,CAAC,OAAO,CAAC,IAAI,CAAC;QACxC,gBAAgB,EAAE,CAAC,CAAC,OAAO,EAAE,CAAC,OAAO,CAAC,IAAI,CAAC;QAC3C,mBAAmB,EAAE,CAAC,CAAC,OAAO,EAAE,CAAC,OAAO,CAAC,IAAI,CAAC;KAC/C,CAAC,CAAC,OAAO,CAAC,EAAE,CAAC;IAEd,yBAAyB;IACzB,KAAK,EAAE,CAAC,CAAC,MAAM,CAAC;QACd,OAAO,EAAE,CAAC,CAAC,OAAO,EAAE,CAAC,OAAO,CAAC,KAAK,CAAC;QACnC,SAAS,EAAE,CAAC,CAAC,MAAM,EAAE,CAAC,OAAO,CAAC,qBAAqB,CAAC;QACpD,SAAS,EAAE,CAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,EAAE,CAAC,OAAO,CAAC,EAAE,CAAC;KAC7C,CAAC,CAAC,OAAO,CAAC,EAAE,CAAC;CACf,CAAC,CAAC;AAQH,4BAA4B;AAC5B,MAAM,CAAC,MAAM,cAAc,GAA2B;IACpD,WAAW,EAAE,GAAG;IAChB,gBAAgB,EAAE,CAAC,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,WAAW,CAAC;IAChE,gBAAgB,EAAE,CAAC,oBAAoB,EAAE,YAAY,EAAE,YAAY,EAAE,mBAAmB,CAAC;IACzF,gBAAgB,EAAE,EAAE;IACpB,SAAS,EAAE,CAAC,QAAQ,EAAE,YAAY,EAAE,YAAY,EAAE,MAAM,CAAC;IACzD,KAAK,EAAE;QACL,YAAY,EAAE,IAAI;QAClB,iBAAiB,EAAE,IAAI;QACvB,eAAe,EAAE,IAAI;QACrB,gBAAgB,EAAE,IAAI;QACtB,WAAW,EAAE,KAAK;KACnB;IACD,aAAa,EAAE,MAAM;IACrB,gBAAgB,EAAE,EAAE;IACpB,cAAc,EAAE;QACd,YAAY,EAAE;YACZ,gBAAgB,EAAE,IAAI;YACtB,cAAc,EAAE,IAAI;YACpB,iBAAiB,EAAE,KAAK;SACzB;QACD,YAAY,EAAE;YACZ,eAAe,EAAE,IAAI;YACrB,cAAc,EAAE,IAAI;YACpB,sBAAsB,EAAE,IAAI;YAC5B,gBAAgB,EAAE,EAAE;SACrB;QACD,UAAU,EAAE;YACV,oBAAoB,EAAE,IAAI;YAC1B,iBAAiB,EAAE,KAAK;YACxB,kBAAkB,EAAE,EAAE;YACtB,eAAe,EAAE,EAAE;SACpB;QACD,gBAAgB,EAAE;YAChB,kBAAkB,EAAE,IAAI;YACxB,sBAAsB,EAAE,IAAI;YAC5B,cAAc,EAAE,KAAK;YACrB,qBAAqB,EAAE,KAAK;SAC7B;KACF;IACD,WAAW,EAAE;QACX,oBAAoB,EAAE,EAAE;QACxB,mBAAmB,EAAE,KAAK;QAC1B,eAAe,EAAE,GAAG;KACrB;IACD,SAAS,EAAE;QACT,qBAAqB,EAAE,IAAI;QAC3B,qBAAqB,EAAE,CAAC;QACxB,aAAa,EAAE,IAAI;QACnB,gBAAgB,EAAE,IAAI;QACtB,mBAAmB,EAAE,IAAI;KAC1B;IACD,KAAK,EAAE;QACL,OAAO,EAAE,KAAK;QACd,SAAS,EAAE,qBAAqB;QAChC,SAAS,EAAE,EAAE;KACd;CACF,CAAC"}