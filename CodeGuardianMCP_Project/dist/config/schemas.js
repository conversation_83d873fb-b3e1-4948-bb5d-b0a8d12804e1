import { z } from 'zod';
import { AnalysisConfigSchema } from '../types.js';
/**
 * Esquemas de configuración avanzados para CodeGuardian MCP
 */
// Configuración específica por lenguaje
export const PythonConfigSchema = z.object({
    python_version: z.string().default('3.8'),
    check_imports: z.boolean().default(true),
    check_syntax: z.boolean().default(true),
    check_style: z.boolean().default(false),
    max_line_length: z.number().int().positive().default(88),
    ignore_missing_imports: z.array(z.string()).default([]),
    virtual_env_path: z.string().optional()
});
export const TypeScriptConfigSchema = z.object({
    typescript_version: z.string().default('5.0'),
    check_types: z.boolean().default(true),
    check_imports: z.boolean().default(true),
    check_syntax: z.boolean().default(true),
    strict_mode: z.boolean().default(true),
    tsconfig_path: z.string().optional(),
    node_modules_path: z.string().optional()
});
export const JavaScriptConfigSchema = z.object({
    ecma_version: z.number().int().min(5).default(2022),
    source_type: z.enum(['script', 'module']).default('module'),
    check_imports: z.boolean().default(true),
    check_syntax: z.boolean().default(true),
    allow_jsx: z.boolean().default(false)
});
// Configuración de reglas específicas
export const RuleConfigSchema = z.object({
    // Reglas de sintaxis
    syntax_rules: z.object({
        check_semicolons: z.boolean().default(true),
        check_brackets: z.boolean().default(true),
        check_indentation: z.boolean().default(false)
    }).default({}),
    // Reglas de imports
    import_rules: z.object({
        check_existence: z.boolean().default(true),
        check_circular: z.boolean().default(true),
        allow_relative_imports: z.boolean().default(true),
        max_import_depth: z.number().int().positive().default(10)
    }).default({}),
    // Reglas de rutas
    path_rules: z.object({
        check_file_existence: z.boolean().default(true),
        check_permissions: z.boolean().default(false),
        allowed_extensions: z.array(z.string()).default([]),
        forbidden_paths: z.array(z.string()).default([])
    }).default({}),
    // Reglas de dependencias
    dependency_rules: z.object({
        check_package_json: z.boolean().default(true),
        check_requirements_txt: z.boolean().default(true),
        check_versions: z.boolean().default(false),
        check_vulnerabilities: z.boolean().default(false)
    }).default({})
});
// Configuración completa extendida
export const ExtendedAnalysisConfigSchema = AnalysisConfigSchema.extend({
    // Configuraciones específicas por lenguaje
    language_configs: z.object({
        python: PythonConfigSchema.optional(),
        typescript: TypeScriptConfigSchema.optional(),
        javascript: JavaScriptConfigSchema.optional()
    }).default({}),
    // Configuración de reglas detalladas
    detailed_rules: RuleConfigSchema.default({}),
    // Configuración de rendimiento
    performance: z.object({
        max_concurrent_files: z.number().int().positive().default(10),
        timeout_per_file_ms: z.number().int().positive().default(30000),
        memory_limit_mb: z.number().int().positive().default(512)
    }).default({}),
    // Configuración de reportes
    reporting: z.object({
        include_code_snippets: z.boolean().default(true),
        snippet_context_lines: z.number().int().nonnegative().default(2),
        group_by_file: z.boolean().default(true),
        sort_by_severity: z.boolean().default(true),
        include_suggestions: z.boolean().default(true)
    }).default({}),
    // Configuración de cache
    cache: z.object({
        enabled: z.boolean().default(false),
        cache_dir: z.string().default('.codeguardian-cache'),
        ttl_hours: z.number().positive().default(24)
    }).default({})
});
// Configuración por defecto
export const DEFAULT_CONFIG = {
    target_path: '.',
    include_patterns: ['**/*.py', '**/*.ts', '**/*.js', '**/*.json'],
    exclude_patterns: ['**/node_modules/**', '**/.git/**', '**/dist/**', '**/__pycache__/**'],
    max_file_size_mb: 10,
    languages: ['python', 'typescript', 'javascript', 'json'],
    rules: {
        syntax_check: true,
        import_validation: true,
        path_validation: true,
        dependency_check: true,
        style_check: false
    },
    output_format: 'both',
    language_configs: {},
    detailed_rules: {
        syntax_rules: {
            check_semicolons: true,
            check_brackets: true,
            check_indentation: false
        },
        import_rules: {
            check_existence: true,
            check_circular: true,
            allow_relative_imports: true,
            max_import_depth: 10
        },
        path_rules: {
            check_file_existence: true,
            check_permissions: false,
            allowed_extensions: [],
            forbidden_paths: []
        },
        dependency_rules: {
            check_package_json: true,
            check_requirements_txt: true,
            check_versions: false,
            check_vulnerabilities: false
        }
    },
    performance: {
        max_concurrent_files: 10,
        timeout_per_file_ms: 30000,
        memory_limit_mb: 512
    },
    reporting: {
        include_code_snippets: true,
        snippet_context_lines: 2,
        group_by_file: true,
        sort_by_severity: true,
        include_suggestions: true
    },
    cache: {
        enabled: false,
        cache_dir: '.codeguardian-cache',
        ttl_hours: 24
    }
};
//# sourceMappingURL=schemas.js.map