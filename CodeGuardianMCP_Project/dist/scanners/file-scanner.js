import { glob } from 'glob';
import * as fs from 'fs-extra';
import * as path from 'path';
/**
 * <PERSON>scáner de archivos para CodeGuardian MCP
 * Responsable de descubrir y filtrar archivos para análisis
 */
export class FileScanner {
    config;
    constructor(config) {
        this.config = config;
    }
    /**
     * Escanea recursivamente un directorio y retorna archivos para análisis
     */
    async scanDirectory(targetPath) {
        const startTime = Date.now();
        console.log(`🔍 Iniciando escaneo de directorio: ${targetPath}`);
        try {
            // Validar que el directorio existe
            const stats = await fs.stat(targetPath);
            if (!stats.isDirectory()) {
                throw new Error(`La ruta especificada no es un directorio: ${targetPath}`);
            }
            // Obtener archivos usando glob patterns
            const files = await this.findFiles(targetPath);
            // Procesar información de cada archivo
            const fileInfos = [];
            for (const filePath of files) {
                try {
                    const fileInfo = await this.getFileInfo(filePath);
                    if (fileInfo) {
                        fileInfos.push(fileInfo);
                    }
                }
                catch (error) {
                    console.warn(`⚠️  Error procesando archivo ${filePath}:`, error);
                }
            }
            const duration = Date.now() - startTime;
            console.log(`✅ Escaneo completado: ${fileInfos.length} archivos encontrados en ${duration}ms`);
            return fileInfos;
        }
        catch (error) {
            console.error(`❌ Error durante el escaneo:`, error);
            throw error;
        }
    }
    /**
     * Encuentra archivos usando patrones glob
     */
    async findFiles(targetPath) {
        const includePatterns = this.config.include_patterns;
        const excludePatterns = this.config.exclude_patterns;
        const allFiles = [];
        // Buscar archivos para cada patrón de inclusión
        for (const pattern of includePatterns) {
            const fullPattern = path.join(targetPath, pattern);
            const files = await glob(fullPattern, {
                ignore: excludePatterns.map(exclude => path.join(targetPath, exclude)),
                nodir: true,
                absolute: true
            });
            allFiles.push(...files);
        }
        // Eliminar duplicados
        return [...new Set(allFiles)];
    }
    /**
     * Obtiene información detallada de un archivo
     */
    async getFileInfo(filePath) {
        try {
            const stats = await fs.stat(filePath);
            // Verificar tamaño máximo
            const sizeMB = stats.size / (1024 * 1024);
            if (sizeMB > this.config.max_file_size_mb) {
                console.warn(`⚠️  Archivo demasiado grande (${sizeMB.toFixed(2)}MB): ${filePath}`);
                return null;
            }
            // Detectar lenguaje
            const language = this.detectLanguage(filePath);
            console.log(`🔍 Debug - Archivo: ${filePath}, Lenguaje detectado: ${language}, Lenguajes config: ${this.config.languages.join(', ')}`);
            if (!language || !this.config.languages.includes(language)) {
                console.log(`❌ Debug - Archivo rechazado: lenguaje ${language} no está en config`);
                return null;
            }
            // Contar líneas
            const content = await fs.readFile(filePath, 'utf-8');
            const linesCount = content.split('\n').length;
            return {
                path: filePath,
                language,
                size_bytes: stats.size,
                lines_count: linesCount,
                encoding: 'utf-8'
            };
        }
        catch (error) {
            console.warn(`⚠️  Error leyendo archivo ${filePath}:`, error);
            return null;
        }
    }
    /**
     * Detecta el lenguaje de programación basado en la extensión del archivo
     */
    detectLanguage(filePath) {
        const ext = path.extname(filePath).toLowerCase();
        const languageMap = {
            '.py': 'python',
            '.pyi': 'python',
            '.ts': 'typescript',
            '.tsx': 'typescript',
            '.js': 'javascript',
            '.jsx': 'javascript',
            '.mjs': 'javascript',
            '.cjs': 'javascript',
            '.json': 'json'
        };
        return languageMap[ext] || null;
    }
    /**
     * Escanea un archivo individual
     */
    async scanFile(filePath) {
        try {
            // Verificar que el archivo existe
            const exists = await fs.pathExists(filePath);
            if (!exists) {
                throw new Error(`El archivo no existe: ${filePath}`);
            }
            return await this.getFileInfo(filePath);
        }
        catch (error) {
            console.error(`❌ Error escaneando archivo ${filePath}:`, error);
            throw error;
        }
    }
    /**
     * Valida si un archivo debe ser incluido en el análisis
     */
    shouldIncludeFile(filePath) {
        const relativePath = path.relative(this.config.target_path, filePath);
        // Verificar patrones de exclusión
        for (const excludePattern of this.config.exclude_patterns) {
            if (this.matchesPattern(relativePath, excludePattern)) {
                return false;
            }
        }
        // Verificar patrones de inclusión
        for (const includePattern of this.config.include_patterns) {
            if (this.matchesPattern(relativePath, includePattern)) {
                return true;
            }
        }
        return false;
    }
    /**
     * Verifica si una ruta coincide con un patrón glob
     */
    matchesPattern(filePath, pattern) {
        // Implementación simple de matching de patrones
        // En una implementación completa, usaríamos una librería como minimatch
        const regexPattern = pattern
            .replace(/\*\*/g, '.*')
            .replace(/\*/g, '[^/]*')
            .replace(/\?/g, '[^/]');
        const regex = new RegExp(`^${regexPattern}$`);
        return regex.test(filePath);
    }
}
//# sourceMappingURL=file-scanner.js.map