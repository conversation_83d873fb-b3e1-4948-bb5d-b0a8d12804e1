import { z } from 'zod';
/**
 * Esquemas de validación usando Zod para CodeGuardian MCP
 */
// Tipos de errores soportados
export const IssueTypeSchema = z.enum([
    'SyntaxError',
    'ImportError',
    'PathError',
    'DependencyError',
    'TypeError',
    'Warning',
    'StyleError',
    'SecurityError'
]);
// Severidad del issue
export const SeveritySchema = z.enum(['error', 'warning', 'info']);
// Lenguajes soportados
export const SupportedLanguageSchema = z.enum([
    'python',
    'typescript',
    'javascript',
    'json'
]);
// Esquema para un issue individual
export const IssueSchema = z.object({
    type: IssueTypeSchema,
    severity: SeveritySchema,
    message: z.string(),
    file_path: z.string(),
    line: z.number().int().positive(),
    column: z.number().int().nonnegative(),
    code_snippet: z.string().optional(),
    suggestion: z.string().optional(),
    rule_id: z.string().optional(),
    language: SupportedLanguageSchema
});
// Esquema para el resumen del análisis
export const AnalysisSummarySchema = z.object({
    files_scanned: z.number().int().nonnegative(),
    errors_found: z.number().int().nonnegative(),
    warnings_found: z.number().int().nonnegative(),
    info_found: z.number().int().nonnegative(),
    languages_detected: z.array(SupportedLanguageSchema),
    scan_duration_ms: z.number().nonnegative(),
    timestamp: z.string().datetime()
});
// Esquema para el reporte completo
export const AnalysisReportSchema = z.object({
    summary: AnalysisSummarySchema,
    issues: z.array(IssueSchema),
    project_path: z.string(),
    config_used: z.record(z.unknown()).optional()
});
// Configuración del análisis
export const AnalysisConfigSchema = z.object({
    target_path: z.string(),
    include_patterns: z.array(z.string()).default(['**/*.py', '**/*.ts', '**/*.js', '**/*.json']),
    exclude_patterns: z.array(z.string()).default(['**/node_modules/**', '**/.git/**', '**/dist/**', '**/__pycache__/**']),
    max_file_size_mb: z.number().positive().default(10),
    languages: z.array(SupportedLanguageSchema).default(['python', 'typescript', 'javascript', 'json']),
    rules: z.object({
        syntax_check: z.boolean().default(true),
        import_validation: z.boolean().default(true),
        path_validation: z.boolean().default(true),
        dependency_check: z.boolean().default(true),
        style_check: z.boolean().default(false)
    }).default({}),
    output_format: z.enum(['json', 'console', 'both']).default('both')
});
// Información de archivo escaneado
export const FileInfoSchema = z.object({
    path: z.string(),
    language: SupportedLanguageSchema,
    size_bytes: z.number().nonnegative(),
    lines_count: z.number().int().nonnegative(),
    encoding: z.string().default('utf-8')
});
// Resultado del análisis de un archivo individual
export const FileAnalysisResultSchema = z.object({
    file_info: FileInfoSchema,
    issues: z.array(IssueSchema),
    analysis_duration_ms: z.number().nonnegative(),
    success: z.boolean()
});
// Parámetros para herramientas MCP
export const AnalyzeProjectParamsSchema = z.object({
    project_path: z.string(),
    config: AnalysisConfigSchema.partial().optional()
});
export const AnalyzeFileParamsSchema = z.object({
    file_path: z.string(),
    language: SupportedLanguageSchema.optional(),
    config: AnalysisConfigSchema.partial().optional()
});
// Respuestas de herramientas MCP
export const ToolResponseSchema = z.object({
    success: z.boolean(),
    data: z.unknown().optional(),
    error: z.string().optional()
});
//# sourceMappingURL=types.js.map