{"version": 3, "file": "language-manager.js", "sourceRoot": "", "sources": ["../../src/analyzers/language-manager.ts"], "names": [], "mappings": "AACA,OAAO,EAAE,cAAc,EAAE,MAAM,sBAAsB,CAAC;AACtD,OAAO,EAAE,kBAAkB,EAAE,MAAM,0BAA0B,CAAC;AAC9D,OAAO,EAAE,YAAY,EAAE,MAAM,oBAAoB,CAAC;AAGlD,OAAO,KAAK,EAAE,MAAM,UAAU,CAAC;AAE/B;;;GAGG;AACH,MAAM,OAAO,eAAe;IAClB,SAAS,CAA4C;IAE7D;QACE,IAAI,CAAC,SAAS,GAAG,IAAI,GAAG,EAAE,CAAC;QAC3B,IAAI,CAAC,mBAAmB,EAAE,CAAC;IAC7B,CAAC;IAED;;OAEG;IACK,mBAAmB;QACzB,sDAAsD;QACtD,IAAI,CAAC,gBAAgB,CAAC,IAAI,cAAc,EAAE,CAAC,CAAC;QAC5C,IAAI,CAAC,gBAAgB,CAAC,IAAI,kBAAkB,EAAE,CAAC,CAAC;QAChD,IAAI,CAAC,gBAAgB,CAAC,IAAI,YAAY,EAAE,CAAC,CAAC;QAE1C,wDAAwD;QACxD,MAAM,UAAU,GAAG,IAAI,kBAAkB,EAAE,CAAC;QAC5C,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,YAAY,EAAE,UAAU,CAAC,CAAC;QAE7C,OAAO,CAAC,GAAG,CAAC,sCAAsC,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,IAAI,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;IACpG,CAAC;IAED;;OAEG;IACK,gBAAgB,CAAC,QAA2B;QAClD,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,QAAQ,CAAC,QAAQ,EAAE,QAAQ,CAAC,CAAC;QAChD,OAAO,CAAC,GAAG,CAAC,iCAAiC,QAAQ,CAAC,QAAQ,EAAE,CAAC,CAAC;IACpE,CAAC;IAED;;OAEG;IACH,WAAW,CAAC,QAA2B;QACrC,OAAO,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,QAAQ,CAAC,IAAI,IAAI,CAAC;IAC9C,CAAC;IAED;;OAEG;IACH,mBAAmB,CAAC,QAA2B;QAC7C,OAAO,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;IACtC,CAAC;IAED;;OAEG;IACH,qBAAqB;QACnB,OAAO,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,IAAI,EAAE,CAAC,CAAC;IAC3C,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,WAAW,CAAC,QAAkB,EAAE,MAA8B;QAClE,MAAM,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QAE7B,IAAI,CAAC;YACH,OAAO,CAAC,GAAG,CAAC,0BAA0B,QAAQ,CAAC,IAAI,KAAK,QAAQ,CAAC,QAAQ,GAAG,CAAC,CAAC;YAE9E,kCAAkC;YAClC,MAAM,QAAQ,GAAG,IAAI,CAAC,WAAW,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC;YACrD,IAAI,CAAC,QAAQ,EAAE,CAAC;gBACd,MAAM,IAAI,KAAK,CAAC,kDAAkD,QAAQ,CAAC,QAAQ,EAAE,CAAC,CAAC;YACzF,CAAC;YAED,yDAAyD;YACzD,IAAI,CAAC,QAAQ,CAAC,UAAU,CAAC,QAAQ,CAAC,EAAE,CAAC;gBACnC,MAAM,IAAI,KAAK,CAAC,oBAAoB,QAAQ,CAAC,QAAQ,iCAAiC,CAAC,CAAC;YAC1F,CAAC;YAED,gCAAgC;YAChC,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,eAAe,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;YAE1D,uBAAuB;YACvB,MAAM,MAAM,GAAG,MAAM,QAAQ,CAAC,WAAW,CAAC,QAAQ,EAAE,OAAO,EAAE,MAAM,CAAC,CAAC;YAErE,gDAAgD;YAChD,IAAI,MAAM,CAAC,SAAS,CAAC,qBAAqB,EAAE,CAAC;gBAC3C,MAAM,IAAI,CAAC,eAAe,CAAC,MAAM,EAAE,OAAO,EAAE,MAAM,CAAC,SAAS,CAAC,qBAAqB,CAAC,CAAC;YACtF,CAAC;YAED,MAAM,QAAQ,GAAG,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS,CAAC;YAExC,OAAO,CAAC,GAAG,CAAC,0BAA0B,MAAM,CAAC,MAAM,0BAA0B,QAAQ,IAAI,CAAC,CAAC;YAE3F,OAAO;gBACL,SAAS,EAAE,QAAQ;gBACnB,MAAM;gBACN,oBAAoB,EAAE,QAAQ;gBAC9B,OAAO,EAAE,IAAI;aACd,CAAC;QAEJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,QAAQ,GAAG,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS,CAAC;YACxC,MAAM,YAAY,GAAG,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;YAE5E,OAAO,CAAC,KAAK,CAAC,sBAAsB,QAAQ,CAAC,IAAI,KAAK,YAAY,EAAE,CAAC,CAAC;YAEtE,oDAAoD;YACpD,MAAM,UAAU,GAAU;gBACxB,IAAI,EAAE,aAAa;gBACnB,QAAQ,EAAE,OAAO;gBACjB,OAAO,EAAE,8BAA8B,YAAY,EAAE;gBACrD,SAAS,EAAE,QAAQ,CAAC,IAAI;gBACxB,IAAI,EAAE,CAAC;gBACP,MAAM,EAAE,CAAC;gBACT,UAAU,EAAE,gDAAgD;gBAC5D,QAAQ,EAAE,QAAQ,CAAC,QAAQ;aAC5B,CAAC;YAEF,OAAO;gBACL,SAAS,EAAE,QAAQ;gBACnB,MAAM,EAAE,CAAC,UAAU,CAAC;gBACpB,oBAAoB,EAAE,QAAQ;gBAC9B,OAAO,EAAE,KAAK;aACf,CAAC;QACJ,CAAC;IACH,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,YAAY,CAAC,SAAqB,EAAE,MAA8B;QACtE,OAAO,CAAC,GAAG,CAAC,4BAA4B,SAAS,CAAC,MAAM,WAAW,CAAC,CAAC;QAErE,MAAM,aAAa,GAAG,MAAM,CAAC,WAAW,CAAC,oBAAoB,CAAC;QAC9D,MAAM,OAAO,GAAyB,EAAE,CAAC;QAEzC,4DAA4D;QAC5D,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,SAAS,CAAC,MAAM,EAAE,CAAC,IAAI,aAAa,EAAE,CAAC;YACzD,MAAM,KAAK,GAAG,SAAS,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,GAAG,aAAa,CAAC,CAAC;YAEpD,OAAO,CAAC,GAAG,CAAC,sBAAsB,IAAI,CAAC,KAAK,CAAC,CAAC,GAAG,aAAa,CAAC,GAAG,CAAC,IAAI,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,MAAM,GAAG,aAAa,CAAC,KAAK,KAAK,CAAC,MAAM,YAAY,CAAC,CAAC;YAEjJ,MAAM,aAAa,GAAG,KAAK,CAAC,GAAG,CAAC,QAAQ,CAAC,EAAE,CACzC,IAAI,CAAC,sBAAsB,CAAC,QAAQ,EAAE,MAAM,CAAC,CAC9C,CAAC;YAEF,MAAM,YAAY,GAAG,MAAM,OAAO,CAAC,GAAG,CAAC,aAAa,CAAC,CAAC;YACtD,OAAO,CAAC,IAAI,CAAC,GAAG,YAAY,CAAC,CAAC;QAChC,CAAC;QAED,MAAM,WAAW,GAAG,OAAO,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,MAAM,EAAE,EAAE,CAAC,GAAG,GAAG,MAAM,CAAC,MAAM,CAAC,MAAM,EAAE,CAAC,CAAC,CAAC;QACnF,MAAM,kBAAkB,GAAG,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,MAAM,CAAC;QAEjE,OAAO,CAAC,GAAG,CAAC,0BAA0B,kBAAkB,IAAI,SAAS,CAAC,MAAM,uBAAuB,WAAW,iBAAiB,CAAC,CAAC;QAEjI,OAAO,OAAO,CAAC;IACjB,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,sBAAsB,CAAC,QAAkB,EAAE,MAA8B;QACrF,MAAM,SAAS,GAAG,MAAM,CAAC,WAAW,CAAC,mBAAmB,CAAC;QAEzD,OAAO,OAAO,CAAC,IAAI,CAAC;YAClB,IAAI,CAAC,WAAW,CAAC,QAAQ,EAAE,MAAM,CAAC;YAClC,IAAI,OAAO,CAAqB,CAAC,CAAC,EAAE,MAAM,EAAE,EAAE,CAC5C,UAAU,CAAC,GAAG,EAAE,CAAC,MAAM,CAAC,IAAI,KAAK,CAAC,sBAAsB,SAAS,IAAI,CAAC,CAAC,EAAE,SAAS,CAAC,CACpF;SACF,CAAC,CAAC,KAAK,CAAC,KAAK,CAAC,EAAE;YACf,OAAO,CAAC,IAAI,CAAC,0BAA0B,QAAQ,CAAC,IAAI,KAAK,KAAK,CAAC,OAAO,EAAE,CAAC,CAAC;YAE1E,OAAO;gBACL,SAAS,EAAE,QAAQ;gBACnB,MAAM,EAAE,CAAC;wBACP,IAAI,EAAE,aAAa;wBACnB,QAAQ,EAAE,OAAO;wBACjB,OAAO,EAAE,0BAA0B,KAAK,CAAC,OAAO,EAAE;wBAClD,SAAS,EAAE,QAAQ,CAAC,IAAI;wBACxB,IAAI,EAAE,CAAC;wBACP,MAAM,EAAE,CAAC;wBACT,UAAU,EAAE,oFAAoF;wBAChG,QAAQ,EAAE,QAAQ,CAAC,QAAQ;qBAC5B,CAAC;gBACF,oBAAoB,EAAE,SAAS;gBAC/B,OAAO,EAAE,KAAK;aACf,CAAC;QACJ,CAAC,CAAC,CAAC;IACL,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,eAAe,CAAC,QAAgB;QAC5C,IAAI,CAAC;YACH,MAAM,OAAO,GAAG,MAAM,EAAE,CAAC,QAAQ,CAAC,QAAQ,EAAE,OAAO,CAAC,CAAC;YACrD,OAAO,OAAO,CAAC;QACjB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,KAAK,YAAY,KAAK,IAAI,MAAM,IAAI,KAAK,EAAE,CAAC;gBAC9C,IAAI,KAAK,CAAC,IAAI,KAAK,QAAQ,EAAE,CAAC;oBAC5B,MAAM,IAAI,KAAK,CAAC,0BAA0B,QAAQ,EAAE,CAAC,CAAC;gBACxD,CAAC;qBAAM,IAAI,KAAK,CAAC,IAAI,KAAK,QAAQ,EAAE,CAAC;oBACnC,MAAM,IAAI,KAAK,CAAC,sCAAsC,QAAQ,EAAE,CAAC,CAAC;gBACpE,CAAC;qBAAM,IAAI,KAAK,CAAC,IAAI,KAAK,QAAQ,EAAE,CAAC;oBACnC,MAAM,IAAI,KAAK,CAAC,4CAA4C,QAAQ,EAAE,CAAC,CAAC;gBAC1E,CAAC;YACH,CAAC;YACD,MAAM,IAAI,KAAK,CAAC,yBAAyB,QAAQ,KAAK,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC;QAClH,CAAC;IACH,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,eAAe,CAAC,MAAe,EAAE,OAAe,EAAE,YAAoB;QAClF,MAAM,KAAK,GAAG,OAAO,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;QAElC,KAAK,MAAM,KAAK,IAAI,MAAM,EAAE,CAAC;YAC3B,IAAI,CAAC,KAAK,CAAC,YAAY,EAAE,CAAC;gBACxB,MAAM,SAAS,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,KAAK,CAAC,IAAI,GAAG,YAAY,GAAG,CAAC,CAAC,CAAC;gBAC7D,MAAM,OAAO,GAAG,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,EAAE,KAAK,CAAC,IAAI,GAAG,YAAY,GAAG,CAAC,CAAC,CAAC;gBAE1E,MAAM,YAAY,GAAG,KAAK,CAAC,KAAK,CAAC,SAAS,EAAE,OAAO,GAAG,CAAC,CAAC,CAAC;gBACzD,MAAM,OAAO,GAAG,YAAY,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,KAAK,EAAE,EAAE;oBAC/C,MAAM,OAAO,GAAG,SAAS,GAAG,KAAK,GAAG,CAAC,CAAC;oBACtC,MAAM,MAAM,GAAG,OAAO,KAAK,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,OAAO,CAAC;oBAC1D,OAAO,GAAG,OAAO,CAAC,QAAQ,EAAE,CAAC,QAAQ,CAAC,CAAC,CAAC,GAAG,MAAM,GAAG,IAAI,EAAE,CAAC;gBAC7D,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;gBAEd,KAAK,CAAC,YAAY,GAAG,OAAO,CAAC;YAC/B,CAAC;QACH,CAAC;IACH,CAAC;IAED;;OAEG;IACH,gBAAgB;QACd,OAAO;YACL,eAAe,EAAE,IAAI,CAAC,SAAS,CAAC,IAAI;YACpC,mBAAmB,EAAE,IAAI,CAAC,qBAAqB,EAAE;YACjD,gBAAgB,EAAE,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,OAAO,EAAE,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,QAAQ,EAAE,QAAQ,CAAC,EAAE,EAAE,CAAC,CAAC;gBACpF,QAAQ;gBACR,cAAc,EAAE,QAAQ,CAAC,WAAW,CAAC,IAAI;aAC1C,CAAC,CAAC;SACJ,CAAC;IACJ,CAAC;IAED;;OAEG;IACH,sBAAsB,CAAC,QAA2B,EAAE,MAA8B;QAChF,MAAM,MAAM,GAAa,EAAE,CAAC;QAE5B,IAAI,CAAC,IAAI,CAAC,mBAAmB,CAAC,QAAQ,CAAC,EAAE,CAAC;YACxC,MAAM,CAAC,IAAI,CAAC,0BAA0B,QAAQ,EAAE,CAAC,CAAC;YAClD,OAAO,MAAM,CAAC;QAChB,CAAC;QAED,wCAAwC;QACxC,QAAQ,QAAQ,EAAE,CAAC;YACjB,KAAK,QAAQ;gBACX,IAAI,MAAM,CAAC,gBAAgB,CAAC,MAAM,EAAE,cAAc,EAAE,CAAC;oBACnD,MAAM,OAAO,GAAG,MAAM,CAAC,gBAAgB,CAAC,MAAM,CAAC,cAAc,CAAC;oBAC9D,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,OAAO,CAAC,EAAE,CAAC;wBAChC,MAAM,CAAC,IAAI,CAAC,+BAA+B,OAAO,EAAE,CAAC,CAAC;oBACxD,CAAC;gBACH,CAAC;gBACD,MAAM;YAER,KAAK,YAAY;gBACf,IAAI,MAAM,CAAC,gBAAgB,CAAC,UAAU,EAAE,kBAAkB,EAAE,CAAC;oBAC3D,MAAM,OAAO,GAAG,MAAM,CAAC,gBAAgB,CAAC,UAAU,CAAC,kBAAkB,CAAC;oBACtE,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,OAAO,CAAC,EAAE,CAAC;wBAChC,MAAM,CAAC,IAAI,CAAC,mCAAmC,OAAO,EAAE,CAAC,CAAC;oBAC5D,CAAC;gBACH,CAAC;gBACD,MAAM;YAER,KAAK,YAAY;gBACf,IAAI,MAAM,CAAC,gBAAgB,CAAC,UAAU,EAAE,YAAY,EAAE,CAAC;oBACrD,MAAM,OAAO,GAAG,MAAM,CAAC,gBAAgB,CAAC,UAAU,CAAC,YAAY,CAAC;oBAChE,IAAI,OAAO,GAAG,CAAC,IAAI,OAAO,GAAG,IAAI,EAAE,CAAC;wBAClC,MAAM,CAAC,IAAI,CAAC,mCAAmC,OAAO,EAAE,CAAC,CAAC;oBAC5D,CAAC;gBACH,CAAC;gBACD,MAAM;QACV,CAAC;QAED,OAAO,MAAM,CAAC;IAChB,CAAC;CACF"}