import { BaseLanguageAnalyzer } from './base-analyzer.js';
import { Issue, SupportedLanguage } from '../types.js';
import { ExtendedAnalysisConfig } from '../config/schemas.js';
/**
 * Analizador específico para archivos JSON
 * Maneja validación de sintaxis, estructura y contenido específico
 */
export declare class JSONAnalyzer extends BaseLanguageAnalyzer {
    readonly language: SupportedLanguage;
    /**
     * Valida la sintaxis JSON del archivo
     */
    validateSyntax(content: string, filePath: string): Promise<Issue[]>;
    /**
     * Valida imports/requires (no aplica para JSON, pero requerido por la interfaz)
     */
    validateImports(_content: string, _filePath: string, _config: ExtendedAnalysisConfig): Promise<Issue[]>;
    /**
     * Valida rutas referenciadas en el archivo JSON
     */
    validatePaths(content: string, filePath: string): Promise<Issue[]>;
    /**
     * Parsea errores de JSON.parse para crear issues detallados
     */
    private parseJSONError;
    /**
     * Convierte una posición de carácter a línea y columna
     */
    private getLineColumnFromPosition;
    /**
     * Genera sugerencias específicas para tokens inesperados
     */
    private getSuggestionForUnexpectedToken;
    /**
     * Valida la estructura general del JSON
     */
    private validateJSONStructure;
    /**
     * Verifica si es un archivo package.json
     */
    private isPackageJSON;
    /**
     * Verifica si es un archivo tsconfig.json
     */
    private isTSConfigJSON;
    /**
     * Valida la estructura específica de package.json
     */
    private validatePackageJSONStructure;
    /**
     * Valida formato semver
     */
    private isValidSemver;
    /**
     * Valida dependencias en package.json
     */
    private validateDependencies;
    /**
     * Valida nombre de paquete npm
     */
    private isValidPackageName;
    /**
     * Valida rango de versión npm
     */
    private isValidVersionRange;
    /**
     * Valida scripts en package.json
     */
    private validateScripts;
    /**
     * Valida rutas específicas en package.json
     */
    private validatePackageJSONPaths;
    /**
     * Busca y valida rutas de archivos en valores JSON
     */
    private findAndValidateFilePaths;
    /**
     * Verifica si un string parece una ruta de archivo
     */
    private looksLikeFilePath;
    /**
     * Valida si una ruta de archivo existe
     */
    private validateFilePath;
    /**
     * Valida estructura de tsconfig.json
     */
    private validateTSConfigStructure;
    /**
     * Valida opciones del compilador TypeScript
     */
    private validateCompilerOptions;
    /**
     * Valida la referencia extends en tsconfig.json
     */
    private validateTSConfigExtends;
}
//# sourceMappingURL=json-analyzer.d.ts.map