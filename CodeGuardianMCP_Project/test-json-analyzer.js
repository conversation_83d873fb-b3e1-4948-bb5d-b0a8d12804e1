import { JSONAnalyzer } from './dist/analyzers/json-analyzer.js';
import { DEFAULT_CONFIG } from './dist/config/schemas.js';
import * as fs from 'fs-extra';

async function testJSONAnalyzer() {
  console.log('🧪 Probando el analizador JSON...');
  
  const analyzer = new JSONAnalyzer();
  
  // Crear un archivo JSON corrupto para probar
  const corruptedJSON = `{
  "name": "test-package",4553543
  "version": "1.0.0",3442342423
  "description": "A test package",
  "main": "index.js",
  "dependencies": {
    "@types/fs-extr344ewfdsxfa": "^11.0asdad.4",ewrwr
    "express": "^4.18.0"
  },qeqwewq
  "scripts": {
    "test": "echo test"
  }
}dsaflaff`;

  console.log('📝 Contenido JSON corrupto:');
  console.log(corruptedJSON);
  console.log('\n🔍 Analizando...');
  
  try {
    const issues = await analyzer.validateSyntax(corruptedJSON, 'test.json');
    
    console.log(`\n✅ Análisis completado. Issues encontrados: ${issues.length}`);
    
    issues.forEach((issue, index) => {
      console.log(`\n📋 Issue ${index + 1}:`);
      console.log(`   Tipo: ${issue.type}`);
      console.log(`   Severidad: ${issue.severity}`);
      console.log(`   Mensaje: ${issue.message}`);
      console.log(`   Línea: ${issue.line}, Columna: ${issue.column}`);
      console.log(`   Sugerencia: ${issue.suggestion}`);
      if (issue.code_snippet) {
        console.log(`   Código:\n${issue.code_snippet}`);
      }
    });
    
  } catch (error) {
    console.error('❌ Error durante el análisis:', error);
  }
}

testJSONAnalyzer().catch(console.error);
