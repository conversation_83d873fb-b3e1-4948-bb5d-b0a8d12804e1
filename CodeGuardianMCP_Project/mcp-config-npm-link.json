{"mcpServers": {"context7": {"command": "npx", "args": ["-y", "@upstash/context7-mcp@latest"]}, "defang": {"command": "npx", "args": ["-y", "defang@latest", "mcp", "serve"]}, "desktop-commander": {"command": "npx", "args": ["-y", "@wonderwhy-er/desktop-commander"]}, "magicui": {"command": "cmd", "args": ["/c", "npx", "-y", "@magicuidesign/mcp@latest"]}, "memory": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-memory"]}, "AME-10 Docs": {"url": "https://gitmcp.io/Ameliorated-LLC/AME-10"}, "sequential-thinking": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-sequential-thinking"]}, "shadcn-ui-server": {"command": "npx", "args": ["-y", "shadcn-ui-mcp-server"]}, "webx": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-puppeteer"]}, "code-analyzer": {"url": "http://localhost:3000/mcp"}, "codeguardian-mcp": {"command": "codeguardian-mcp", "args": []}}}