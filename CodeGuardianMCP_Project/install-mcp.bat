@echo off
REM Script de instalación para CodeGuardian MCP en Windows

echo 🛡️  Instalando CodeGuardian MCP...

REM Verificar que estamos en el directorio correcto
if not exist "package.json" (
    echo ❌ Error: Ejecuta este script desde el directorio CodeGuardianMCP_Project
    exit /b 1
)

REM Compilar el proyecto
echo 📦 Compilando TypeScript...
call npm run build

REM Verificar que la compilación fue exitosa
if not exist "dist\index.js" (
    echo ❌ Error: La compilación falló
    exit /b 1
)

echo ✅ Compilación exitosa

echo.
echo 🔗 Opción 1: Instalación global con npm link
echo Ejecuta los siguientes comandos:
echo   npm link
echo   # Luego usa la configuración: mcp-config-npm-link.json
echo.

echo 📋 Opción 2: Configuración directa
echo Usa la configuración en: mcp-config-updated.json
echo Ruta del ejecutable: %CD%\dist\index.js
echo.

echo 🔧 Configuración para agregar a tu mcp-config.json:
echo.
echo     "codeguardian-mcp": {
echo       "command": "node",
echo       "args": [
echo         "%CD%\dist\index.js"
echo       ],
echo       "env": {
echo         "NODE_ENV": "production"
echo       }
echo     }
echo.

echo ✅ CodeGuardian MCP está listo para usar!
echo.
echo 📚 Herramientas disponibles:
echo   • analyze_project - Analizar proyecto completo
echo   • analyze_file - Analizar archivo individual  
echo   • get_supported_languages - Listar lenguajes soportados
echo   • validate_config - Validar configuración
echo   • get_analyzer_stats - Estadísticas de analizadores
echo.
echo 📖 Ver ejemplos de uso en: examples\usage-examples.md
echo ⚙️  Ver configuración de ejemplo en: examples\example-config.json

pause
