# 🛡️ CodeGuardian MCP - Instrucciones de Configuración

## ✅ Estado del Proyecto

**CodeGuardian MCP ha sido desarrollado exitosamente y está listo para usar!**

### 📋 Lo que se ha completado:

1. ✅ **Arquitectura MCP completa** - Servidor MCP funcional con todas las herramientas
2. ✅ **Analizadores de lenguaje** - Python, TypeScript, JavaScript
3. ✅ **Sistema de reportes** - JSON y consola con formato legible
4. ✅ **Validación con Zod** - Esquemas robustos para configuración y datos
5. ✅ **Compilación exitosa** - TypeScript compilado a JavaScript
6. ✅ **Configuración MCP** - Listo para integrar con desktop-commander

## 🚀 Configuración Rápida

### Opción 1: Configuración Directa (Recomendada)

Copia esta configuración a tu archivo MCP principal:

```json
"codeguardian-mcp": {
  "command": "node",
  "args": [
    "/home/<USER>/projects/CodeGuardianMCP_Project/dist/index.js"
  ],
  "env": {
    "NODE_ENV": "production"
  }
}
```

### Opción 2: Instalación Global

```bash
cd /home/<USER>/projects/CodeGuardianMCP_Project
sudo npm link
```

Luego usa esta configuración:

```json
"codeguardian-mcp": {
  "command": "codeguardian-mcp",
  "args": []
}
```

## 📁 Archivos de Configuración Disponibles

1. **`mcp-config-updated.json`** - Tu configuración actual + CodeGuardian MCP
2. **`mcp-config-npm-link.json`** - Configuración para instalación global
3. **`examples/example-config.json`** - Configuración detallada de ejemplo

## 🛠️ Herramientas MCP Disponibles

### 1. `analyze_project`
Analiza un proyecto completo de código fuente.

**Ejemplo de uso:**
```json
{
  "name": "analyze_project",
  "arguments": {
    "project_path": "/path/to/your/project",
    "config": {
      "languages": ["python", "typescript"],
      "output_format": "both"
    }
  }
}
```

### 2. `analyze_file`
Analiza un archivo individual.

**Ejemplo de uso:**
```json
{
  "name": "analyze_file",
  "arguments": {
    "file_path": "/path/to/file.py"
  }
}
```

### 3. `get_supported_languages`
Lista los lenguajes soportados.

### 4. `validate_config`
Valida una configuración de análisis.

### 5. `get_analyzer_stats`
Obtiene estadísticas de los analizadores.

## 🧪 Prueba de Funcionamiento

### Verificar que el MCP funciona:

```bash
cd /home/<USER>/projects/CodeGuardianMCP_Project
node dist/index.js --help
```

### Probar análisis con archivos de ejemplo:

Los archivos en `examples/test-files/` contienen errores intencionalmente para demostrar las capacidades del analizador:

- `example.py` - Errores de Python (imports faltantes, sintaxis, etc.)
- `example.ts` - Errores de TypeScript (tipos any, imports faltantes, etc.)

## 🔧 Configuración Avanzada

### Configuración Personalizada

Puedes personalizar el comportamiento del analizador:

```json
{
  "name": "analyze_project",
  "arguments": {
    "project_path": "./src",
    "config": {
      "languages": ["python", "typescript"],
      "include_patterns": ["**/*.py", "**/*.ts"],
      "exclude_patterns": ["**/test/**", "**/node_modules/**"],
      "rules": {
        "syntax_check": true,
        "import_validation": true,
        "path_validation": true,
        "style_check": false
      },
      "performance": {
        "max_concurrent_files": 5,
        "timeout_per_file_ms": 15000
      },
      "reporting": {
        "include_code_snippets": true,
        "sort_by_severity": true
      }
    }
  }
}
```

## 📊 Tipos de Análisis Soportados

### Python (.py, .pyi)
- ✅ Análisis de sintaxis básica
- ✅ Validación de imports (estándar, relativos, terceros)
- ✅ Verificación de indentación
- ✅ Paréntesis balanceados
- ✅ Estructuras de control (dos puntos)
- ✅ Validación de rutas de archivos
- ✅ Detección de encoding para caracteres no-ASCII

### TypeScript (.ts, .tsx)
- ✅ Análisis de sintaxis con Acorn
- ✅ Validación de imports ES6 y CommonJS
- ✅ Verificación de dependencias en package.json
- ✅ Detección de uso de tipo 'any'
- ✅ Validación de rutas de archivos
- ✅ Análisis específico para JSX/React

### JavaScript (.js, .jsx, .mjs, .cjs)
- ✅ Análisis de sintaxis ES6+
- ✅ Validación de imports y requires
- ✅ Verificación de dependencias
- ✅ Soporte para JSX
- ✅ Validación de rutas de archivos

## 🎯 Próximos Pasos

1. **Integrar con tu configuración MCP actual**
2. **Probar con tus proyectos reales**
3. **Personalizar configuraciones según tus necesidades**
4. **Explorar los ejemplos en `examples/`**

## 🆘 Solución de Problemas

### Error: "require is not defined"
- ✅ **Solucionado** - El proyecto usa ES modules correctamente

### Error: "Cannot find module"
- Ejecuta `npm install` en el directorio del proyecto
- Verifica que `dist/index.js` existe (ejecuta `npm run build`)

### Error de permisos
- Ejecuta `chmod +x dist/index.js`
- Para instalación global: `sudo npm link`

## 📚 Documentación Adicional

- **`README.md`** - Documentación completa del proyecto
- **`examples/usage-examples.md`** - Ejemplos detallados de uso
- **`examples/example-config.json`** - Configuración de ejemplo completa

---

**¡CodeGuardian MCP está listo para elevar la calidad de tu código! 🛡️✨**
