#!/bin/bash

# Script de instalación para CodeGuardian MCP
# Este script configura CodeGuardian MCP para uso con desktop-commander

set -e

echo "🛡️  Instalando CodeGuardian MCP..."

# Verificar que estamos en el directorio correcto
if [ ! -f "package.json" ]; then
    echo "❌ Error: Ejecuta este script desde el directorio CodeGuardianMCP_Project"
    exit 1
fi

# Compilar el proyecto
echo "📦 Compilando TypeScript..."
npm run build

# Verificar que la compilación fue exitosa
if [ ! -f "dist/index.js" ]; then
    echo "❌ Error: La compilación falló"
    exit 1
fi

echo "✅ Compilación exitosa"

# Hacer el archivo ejecutable
chmod +x dist/index.js

# Opción 1: Instalación global con npm link
echo ""
echo "🔗 Opción 1: Instalación global con npm link"
echo "Ejecuta los siguientes comandos:"
echo "  sudo npm link"
echo "  # Luego usa la configuración: mcp-config-npm-link.json"
echo ""

# Opción 2: Configuración directa
echo "📋 Opción 2: Configuración directa"
echo "Usa la configuración en: mcp-config-updated.json"
echo "Ruta del ejecutable: $(pwd)/dist/index.js"
echo ""

# Mostrar configuración para copiar
echo "🔧 Configuración para agregar a tu mcp-config.json:"
echo ""
cat << EOF
    "codeguardian-mcp": {
      "command": "node",
      "args": [
        "$(pwd)/dist/index.js"
      ],
      "env": {
        "NODE_ENV": "production"
      }
    }
EOF

echo ""
echo "✅ CodeGuardian MCP está listo para usar!"
echo ""
echo "📚 Herramientas disponibles:"
echo "  • analyze_project - Analizar proyecto completo"
echo "  • analyze_file - Analizar archivo individual"
echo "  • get_supported_languages - Listar lenguajes soportados"
echo "  • validate_config - Validar configuración"
echo "  • get_analyzer_stats - Estadísticas de analizadores"
echo ""
echo "📖 Ver ejemplos de uso en: examples/usage-examples.md"
echo "⚙️  Ver configuración de ejemplo en: examples/example-config.json"
